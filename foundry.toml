[profile.default]
ast = true
build_info = true
evm_version = "cancun"
extra_output = ["storageLayout"]
ffi = true
fs_permissions = [{access = "read", path = "./out"}]
libs = ["lib"]
out = "out"
solc = "0.8.28"
src = "src"
test = 'test'
via_ir = true

gas_reports = ["*"]
libraries = [
  # "src/libraries/IntentTypesLib.sol:IntentTypesLib:0x79789af76357B94A0364415ceBa60fE59981Aac4",
  # "src/libraries/InteropLibV2.sol:InteropLibV2:0xf02D599FEBeac283e563C97eC7B6294Bc3250e3d",
  # "src/libraries/IntentProcessorLib.sol:IntentProcessorLib:0xeF0dbf7561925F70f0eb8045f9C6982D4345f3d2",
  # "src/libraries/UtilsV2.sol:UtilsV2:0x07a104c8D7d66682891B23FC81c7e2cbbb665800",
  # "src/libraries/ValidationLibV2.sol:ValidationLibV2:0x50c7c671FfaC0bdAa80DdBCD81e043d3443cd94D",
  # "src/libraries/IntentLibV2.sol:IntentLibV2:0x41F540E00d980AD1904681dF220e30A8981e9C5c",
  # "src/libraries/MessageHandler.sol:MessageHandler:0x664fDF47d3373791d74e59F075C0DEbe8C147116"
  ]

remappings = [
    '@layerzerolabs/oapp-evm/=lib/devtools/packages/oapp-evm/',
    '@layerzerolabs/lz-evm-protocol-v2/=lib/layerzero-v2/packages/layerzero-v2/evm/protocol',
    '@layerzerolabs/lz-evm-messagelib-v2/=lib/layerzero-v2/packages/layerzero-v2/evm/messagelib',
    # '@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/',
    '@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/',
    'solidity-bytes-utils/=lib/solidity-bytes-utils/',
    '@layerzerolabs-upgradeable/=lib/LayerZero-v2-upgradeable/',
    '@aave/=lib/aave-v3-origin/src/contracts/',
    '@lido/=lib/lido-core/contracts/',

    '@layerzerolabs/test-devtools-evm-foundry/=lib/devtools/packages/test-devtools-evm-foundry',
    '@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/',
    '@layerzerolabs/lz-evm-v1-0.7/=lib/LayerZero-v1/',
    'solidity-stringutils/=lib/solidity-stringutils/',
    '@debridge-solana/=lib/evm-sol-serializer/'
]

[rpc_endpoints]
arbitrum-sepolia = "${ARBITRUM_RPC_URL}"
inclusivefinancetestnet = "${INCLUSIVE_FINANCE_TESTNET_RPC_URL}"

[etherscan]
arbitrum-sepolia = {key = "${ARBIRUMSCAN_API_KEY}"}

[fmt]
bracket_spacing = true
line_length = 100
multiline_func_header = 'params_first'
single_line_statement_blocks = 'preserve'
sort_imports = true
wrap_comments = true

[profile.coverage]
via_ir = true
optimizer = true
optimizer_runs = 1
# Use minimal optimization for coverage to avoid stack too deep errors
# This profile is specifically for running coverage reports

[profile.ci]
via_ir = true
optimizer = true
optimizer_runs = 200
# CI profile used in GitHub Actions

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
