{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "Bash(forge build:*)", "Bash(forge test:*)", "Bash(grep:*)", "Bash(rg:*)", "WebFetch(domain:docs.uniswap.org)", "Bash(gh api:*)", "WebFetch(domain:github.com)", "Bash(forge coverage:*)", "mcp__serena__initial_instructions", "mcp__serena__activate_project", "mcp__serena__find_file", "mcp__serena__read_file", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(cast call:*)", "<PERSON><PERSON>(cast abi-decode:*)", "<PERSON><PERSON>(forge script:*)", "<PERSON><PERSON>(cast:*)", "mcp__serena__check_onboarding_performed", "mcp__serena__get_symbols_overview", "mcp__serena__search_for_pattern", "mcp__serena__list_dir", "mcp__serena__find_symbol", "Bash(FOUNDRY_ETH_RPC_URL=https://base.drpc.org cast interface ******************************************)", "Bash(rm:*)", "mcp__serena__replace_symbol_body", "mcp__serena__onboarding", "mcp__serena__write_memory", "mcp__serena__replace_regex", "mcp__serena__think_about_task_adherence", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__summarize_changes", "Bash(FOUNDRY_PROFILE=coverage forge coverage)", "Bash(FOUNDRY_PROFILE=coverage forge coverage --ir-minimum)", "Bash(gh repo view:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(gh run view:*)", "Bash(FOUNDRY_PROFILE=ci forge coverage --report lcov --report summary --no-match-coverage \"(test|mock|node_modules|script|Prev|LZ|Token)\" --no-match-test testFork --ir-minimum)", "Bash(FOUNDRY_PROFILE=ci forge coverage --report lcov --report summary --no-match-coverage \"(test|mock|node_modules|script|Prev|LZ|Token|IntegrationTest)\" --no-match-test testFork --ir-minimum)", "<PERSON><PERSON>(mv:*)", "Bash(FOUNDRY_PROFILE=ci forge coverage --report lcov --report summary --no-match-coverage \"(test|mock|node_modules|script|Prev|LZ|Token)\" --no-match-test testFork --ir-minimum)", "Bash(FOUNDRY_PROFILE=ci forge coverage --report lcov --report summary --no-match-coverage \"(test|mock|node_modules|script|Prev|LZ|Token)\" --no-match-test testFork --ir-minimum)", "Bash(FOUNDRY_PROFILE=ci forge coverage --report summary --no-match-coverage \"(test|mock|node_modules|script|Prev|LZ|Token)\" --no-match-test testFork --ir-minimum 2 >& 1)", "Bash(FOUNDRY_PROFILE=ci forge coverage --report summary --no-match-coverage \"(test|mock|node_modules|script|Prev|LZ|Token)\" --no-match-test testFork --ir-minimum 2 >& 1)", "Bash(FOUNDRY_PROFILE=ci forge coverage --report summary --no-match-coverage \"(test|mock|node_modules|script|Prev|LZ|Token)\" --no-match-test testFork --ir-minimum)", "Bash(FOUNDRY_PROFILE=ci forge coverage --report summary --no-match-coverage \"(test|mock|node_modules|script|Prev|LZ|Token)\" --no-match-test testFork --ir-minimum 2 >& 1)", "Bash(git commit:*)"], "deny": []}}