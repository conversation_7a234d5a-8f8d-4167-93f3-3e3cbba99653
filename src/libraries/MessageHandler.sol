// SPDX-License-Identifier: UNLICENSED
pragma solidity =0.8.28;

import { IDLNDestination } from "../interfaces/IDLNDestination.sol";
import { IDLNSource } from "../interfaces/IDLNSource.sol";

import { StakingManager } from "../StakingManager.sol";
import { IMailbox } from "../interfaces/IMailbox.sol";
import { IMockLN } from "../interfaces/IMockLN.sol";

import { INonfungiblePositionManager } from "../interfaces/INonfungiblePositionManager.sol";
import { ISwapRouter } from "../interfaces/ISwapRouter.sol";
import { IVault } from "../interfaces/IVault.sol";
import { IWETH9 } from "../interfaces/IWETH9.sol";
import { IntentLib } from "./IntentLib.sol";
import { IntentTypesLib } from "./IntentTypesLib.sol";

import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// Constants For DeBridge

// DeBridge chain ID for Solana
uint256 constant DEBRIDGE_SOLANA_CHAIN_ID = 7565164;
// Solana chain used in our system
uint256 constant SOLANA_CHAIN_ID = 1399811149;
bytes8 constant SOLANA_HOOK_INSTRUCTION_INDEX = bytes8(uint64(4));

// Program ID
bytes32 constant SOLANA_VAULTS_PROGRAM_ID =
    bytes32(0xa972870ea031640bca1cc352aadc79a08df764d9da5b19702717c7e8b8804e57);
// Vault Params
bytes32 constant SOLANA_VAULT_PARAMS =
    bytes32(0xfaac82a40898eec8f85de0411e9bca511fe58bcddf74d77864b4964f6d963c43);
// Ack messages
bytes32 constant SOLANA_ACK_MESSAGES =
    bytes32(0x482c2ff9cbd59335ec6c516e30ce834f63a4e9d12af5c772402203bc5a8169eb);
// system program
bytes32 constant SOLANA_SYSTEM_PROGRAM =
    bytes32(0x0000000000000000000000000000000000000000000000000000000000000000);
// token program
bytes32 constant SOLANA_TOKEN_PROGRAM =
    bytes32(0x06ddf6e1d765a193d9cbe146ceeb79ac1cb485ed5f5b37913a8cf5857eff00a9);
// associated token program
bytes32 constant SOLANA_ASSOCIATED_TOKEN_PROGRAM =
    bytes32(0x8c97258f4e2489f1bb3d1029148e0d830b5a1399daff1084048e7bd8dbe9f859);
// Wallet Placeholder
bytes32 constant SOLANA_WALLET_PLACEHOLDER =
    bytes32(0xfd97b70d573d364ef44769540777b1ecdc21b88ff7def38c45d020e271c589dc);
// Submission Placeholder
bytes32 constant SOLANA_SUBMISSION_PLACEHOLDER =
    bytes32(0x62584959deb8a728a91cebdc187b545d920479265052145f31fb80c73fac5aea);
// Authority Placeholder
bytes32 constant SOLANA_AUTHORITY_PLACEHOLDER =
    bytes32(0x1968562fef0aab1b1d8f99d44306595cd4ba41d7cc899c007a774d23ad702ff6);
// Debridge Solana Executor Contract ( owned by debridge )
bytes32 constant DEBRIDGE_SOLANA_EXECUTOR_CONTRACT =
    bytes32(0x09b966be097f46dcf58ebaae2365f56b74cd218a2b8c62468ffa4c53d484b053);

/**
 * @title MessageHandler
 * @dev Library for handling message dispatched from the IntentProcessorV2 contract.
 * This library provides functions to route messages based on different solution types
 * and manage user balances and acknowledgments.
 */
library MessageHandler {
    using IntentLib for address;
    using IntentLib for bytes32;
    using IntentLib for bytes;
    using SafeERC20 for IERC20;

    uint32 public constant DEBRIDGE_REFERRAL_CODE = 31569;

    error InvalidOrderType();
    error InvalidLiquidityNetwork();
    error InvalidSolutionType();
    error TokenMismatch(address received, address expected);
    error AmountMismatch(uint256 received, uint256 expected);

    /**
     * @dev Retrieves the receiver address based on the provided receiver data.
     * @param receiver The receiver data containing the enum variant and associated data.
     * @return receiverAddress The address of the receiver.
     */
    function _getReceiverAddress(
        IntentTypesLib.SolidityReceiver memory receiver
    ) internal pure returns (bytes32 receiverAddress) {
        if (receiver.enumVariant == IntentTypesLib.ReceiverEnum.UserAddress) {
            IntentTypesLib.ReceiverUserAddressData memory userAddressData =
                abi.decode(receiver.data, (IntentTypesLib.ReceiverUserAddressData));
            receiverAddress = userAddressData.userAddress;
        } else if (receiver.enumVariant == IntentTypesLib.ReceiverEnum.Vault) {
            IntentTypesLib.ReceiverVaultData memory vaultData =
                abi.decode(receiver.data, (IntentTypesLib.ReceiverVaultData));
            receiverAddress = vaultData.poolAddress;
        } else { }
    }

    /**
     * @dev Routes a message based on the provided parameters.
     * @param _message The message to be routed.
     * @param deBridgeDLNSource The source of the deBridge DLN.
     * @param mockedLNExternalCallAdaptor The address of the mocked LN external call adaptor.
     * @param stakingManagerCallAdaptor The address of the staking manager call adaptor.
     * @param provider The interop provider for the message.
     * @param nativeTokenWrapperAddress The token address of the WETH9 contract.
     * @param usdcTokenAddress The token address of the USDC token.
     * @return sendAck A boolean indicating if an acknowledgment should be sent.
     * @return orderId The ID of the order associated with the message.
     * @return result A boolean indicating the success of the routing operation.
     * @return errorMessage A string containing any error message if the operation fails.
     * @return metadata Metadata associated with the acknowledgment.
     */
    function route(
        bytes memory _message,
        IDLNSource deBridgeDLNSource,
        address mockedLNExternalCallAdaptor,
        address stakingManagerCallAdaptor,
        IntentTypesLib.InteropProvider provider,
        address nativeTokenWrapperAddress,
        address usdcTokenAddress
    )
        public
        returns (
            bool sendAck,
            uint64 orderId,
            bool result,
            string memory errorMessage,
            IntentTypesLib.SolidityAcknowledgementMetadata memory metadata
        )
    {
        IntentTypesLib.SolidityVaultBoundMessage memory order =
            abi.decode(_message, (IntentTypesLib.SolidityVaultBoundMessage));

        metadata.enumVariant = IntentTypesLib.IntentPayloadEnum.Transact;
        metadata.data = bytes("");

        if (order.enumVariant == IntentTypesLib.VaultBoundMessageEnum.PlaceOrder) {
            IntentTypesLib.VaultBoundMessagePlaceOrderData memory orderData =
                abi.decode(order.data, (IntentTypesLib.VaultBoundMessagePlaceOrderData));
            bytes32 receiverAddress = _getReceiverAddress(orderData.order.receiver);
            orderId = orderData.order.orderId;
            // The amountIn would be different if the tokenIn is a receipt token since the amountIn
            // that is passed in the order is the amount of shares. In other cases, the amountIn
            // would
            // be same as the one in the order.
            uint256 amountIn = orderData.order.amountIn;
            // If the receipt token ( tokens minted by staking protocols ) is transferred, then
            // a call has to be made to the staking manager to update the user position.
            if (orderData.order.solution.enumVariant != IntentTypesLib.SolutionTypeEnum.Stake) {
                address token = orderData.order.tokenIn.bytes32ToAddress();
                if (token != address(0)) {
                    (uint256 totalShares, address depositTokenAddress) = StakingManager(
                        payable(stakingManagerCallAdaptor)
                    ).receiptTokenToTokenInfo(token);
                    if (depositTokenAddress != address(0)) {
                        amountIn = StakingManager(payable(stakingManagerCallAdaptor))
                            .transferReceiptToken(token, amountIn);
                    }
                }
            }

            if (
                orderData.order.solution.enumVariant
                    == IntentTypesLib.SolutionTypeEnum.LocalTransfer
            ) {
                // For local transfer, the amountIn is equal to amountOut and tokenIn is equal to
                // tokenOut
                address token = orderData.order.tokenIn.bytes32ToAddress();
                uint256 balance = IERC20(token).balanceOf(address(this));
                sendAck = true;
                if (
                    balance >= orderData.order.amountOut
                        && orderData.order.receiver.enumVariant
                            == IntentTypesLib.ReceiverEnum.UserAddress
                ) {
                    address receiver = receiverAddress.bytes32ToAddress();
                    transferErc20Tokens(nativeTokenWrapperAddress, token, amountIn, receiver);
                    errorMessage = "";
                    result = true;
                } else {
                    result = false;
                    errorMessage = "Invalid order for Local Transfer";
                }
            } else if (
                orderData.order.solution.enumVariant == IntentTypesLib.SolutionTypeEnum.LocalSwap
            ) {
                storeOrderHash(
                    orderData.order,
                    receiverAddress,
                    amountIn,
                    mockedLNExternalCallAdaptor,
                    usdcTokenAddress,
                    bytes("")
                );
            } else if (
                orderData.order.solution.enumVariant == IntentTypesLib.SolutionTypeEnum.CrossChain
            ) {
                IntentTypesLib.SolidityLiquidityNetwork memory liquidityNetwork = abi.decode(
                    orderData.order.solution.data, (IntentTypesLib.SolutionTypeCrossChainData)
                ).liquidityNetwork;
                if (liquidityNetwork.enumVariant == IntentTypesLib.LiquidityNetworkEnum.DLN) {
                    IntentTypesLib.LiquidityNetworkDLNData memory liquidityNetworkData =
                        abi.decode(liquidityNetwork.data, (IntentTypesLib.LiquidityNetworkDLNData));
                    IMockLN.DebridgeMetadata memory debridgeMetadata = IMockLN.DebridgeMetadata(
                        liquidityNetworkData.destinationVaultAddress,
                        orderData.order.tokenOut,
                        orderData.order.amountOut,
                        orderData.order.destinationChainId,
                        receiverAddress
                    );
                    if (orderData.order.tokenIn.bytes32ToAddress() == usdcTokenAddress) {
                        // Approve maximum tokens to the mockLN contract to avoid re-approval
                        // every time the order is placed since mockLN contract is trusted by the
                        // vault.
                        checkAndApproveAllowance(
                            orderData.order.tokenIn.bytes32ToAddress(),
                            mockedLNExternalCallAdaptor,
                            amountIn,
                            type(uint256).max
                        );
                        IMockLN(mockedLNExternalCallAdaptor).sendDLNOrderFromVault(
                            debridgeMetadata,
                            amountIn,
                            orderData.order.tokenIn.bytes32ToAddress(),
                            provider,
                            orderData.order.orderId
                        );
                    } else {
                        storeOrderHash(
                            orderData.order,
                            receiverAddress,
                            amountIn,
                            mockedLNExternalCallAdaptor,
                            usdcTokenAddress,
                            abi.encode(debridgeMetadata)
                        );
                    }
                } else if (
                    liquidityNetwork.enumVariant == IntentTypesLib.LiquidityNetworkEnum.MockedLN
                ) {
                    IntentTypesLib.LiquidityNetworkMockedLNData memory mockedLnData = abi.decode(
                        liquidityNetwork.data, (IntentTypesLib.LiquidityNetworkMockedLNData)
                    );
                    IMockLN.MockLNOrder memory mockedLnOrder = IMockLN.MockLNOrder(
                        orderData.order.orderId,
                        orderData.order.tokenIn,
                        orderData.order.tokenOut,
                        amountIn,
                        orderData.order.amountOut,
                        mockedLnData.destinationChainId,
                        mockedLnData.sourceChainId,
                        receiverAddress,
                        mockedLnData.filler,
                        orderData.order.multiLeg,
                        mockedLnData.stableCoinAddress,
                        bytes(""),
                        orderData.order.timeoutUnixTimestampInSec
                    );
                    bytes32 orderHash = keccak256(abi.encode(mockedLnOrder));
                    address tokenInAddress = mockedLnOrder.tokenIn.bytes32ToAddress();
                    if (tokenInAddress != address(0)) {
                        uint256 existingAllowance = IERC20(tokenInAddress).allowance(
                            address(this), mockedLNExternalCallAdaptor
                        );
                        // Approve maximum tokens to the mockLN contract to avoid re-approval
                        // every time the order is placed since mockLN contract is trusted by the
                        // vault.
                        if (existingAllowance < mockedLnOrder.amountIn) {
                            safeApprove(
                                IERC20(tokenInAddress),
                                mockedLNExternalCallAdaptor,
                                type(uint256).max,
                                existingAllowance
                            );
                        }
                    }
                    IMockLN(mockedLNExternalCallAdaptor).storeOrderHash(
                        mockedLnOrder.orderId, orderHash
                    );
                } else {
                    revert InvalidLiquidityNetwork();
                }
            } else if (
                orderData.order.solution.enumVariant == IntentTypesLib.SolutionTypeEnum.Stake
            ) {
                sendAck = true;
                result = true;
                uint256 tokensReceived;
                address tokenReceivedAddress;
                uint256 requestId;
                IntentTypesLib.SolutionTypeStakeData memory stakeData = abi.decode(
                    orderData.order.solution.data, (IntentTypesLib.SolutionTypeStakeData)
                );

                if (stakeData.stakeAction == IntentTypesLib.StakeActionEnum.Deposit) {
                    (tokenReceivedAddress, tokensReceived) = depositStake(
                        stakingManagerCallAdaptor, nativeTokenWrapperAddress, orderData, stakeData
                    );
                } else if (stakeData.stakeAction == IntentTypesLib.StakeActionEnum.Withdraw) {
                    uint256 amount = StakingManager(payable(stakingManagerCallAdaptor))
                        .calculateAmountFromShares(
                        orderData.order.tokenIn.bytes32ToAddress(), orderData.order.amountIn
                    );
                    uint256 currentAllowance = IERC20(orderData.order.tokenIn.bytes32ToAddress())
                        .allowance(address(this), stakingManagerCallAdaptor);
                    safeApprove(
                        IERC20(orderData.order.tokenIn.bytes32ToAddress()),
                        stakingManagerCallAdaptor,
                        amount,
                        currentAllowance
                    );
                    (tokenReceivedAddress, tokensReceived, requestId) = StakingManager(
                        payable(stakingManagerCallAdaptor)
                    ).withdraw(
                        orderData.order.amountIn,
                        orderData.order.tokenIn.bytes32ToAddress(),
                        stakeData.stakeProtocol,
                        stakeData.stakingPoolAddress.bytes32ToAddress()
                    );

                    // if necessary, wrap native tokens
                    if (
                        tokenReceivedAddress == address(******************************************)
                            && tokensReceived > 0
                    ) {
                        IWETH9(nativeTokenWrapperAddress).deposit{ value: tokensReceived }();
                    }
                }
                metadata.enumVariant = IntentTypesLib.IntentPayloadEnum.Stake;
                metadata.data = abi.encode(
                    IntentTypesLib.AcknowledgementMetadataStake({
                        receiptTokenAddress: tokenReceivedAddress.addressToBytes32(),
                        isTokenCredited: true,
                        amountCredited: tokensReceived,
                        requestId: requestId
                    })
                );
            } else if (
                orderData.order.solution.enumVariant
                    == IntentTypesLib.SolutionTypeEnum.LaunchpadSwap
            ) {
                sendAck = true;
                // Execute Uniswap v3 swap directly
                uint256 actualAmountOut;
                (result, actualAmountOut) = executeUniswapV3Swap(
                    orderData.order, receiverAddress, amountIn, nativeTokenWrapperAddress
                );

                metadata.enumVariant = IntentTypesLib.IntentPayloadEnum.LaunchpadSwap;
                metadata.data = abi.encode(
                    IntentTypesLib.AcknowledgementMetadataLaunchpadSwap({
                        receivedAmount: actualAmountOut
                    })
                );

                if (!result) {
                    errorMessage = "Uniswap v3 swap failed";
                }
            } else if (
                orderData.order.solution.enumVariant
                    == IntentTypesLib.SolutionTypeEnum.LaunchpadAddLiquidity
            ) {
                sendAck = true;
                // Execute Uniswap v3 add liquidity directly
                uint256 actualTokenId;
                uint256 actualAmount0;
                uint256 actualAmount1;
                (result, actualTokenId, actualAmount0, actualAmount1) = executeUniswapV3AddLiquidity(
                    orderData.order, receiverAddress, amountIn, nativeTokenWrapperAddress
                );

                metadata.enumVariant = IntentTypesLib.IntentPayloadEnum.LaunchpadAddLiquidity;
                metadata.data = abi.encode(
                    IntentTypesLib.AcknowledgementMetadataLaunchpadAddLiquidity({
                        receiptNFTAddress: IVault(address(this)).uniswapV3PositionManager()
                            .addressToBytes32(),
                        tokenId: actualTokenId,
                        amount0: actualAmount0,
                        amount1: actualAmount1
                    })
                );

                if (!result) {
                    errorMessage = "Uniswap v3 add liquidity failed";
                }
            } else if (
                orderData.order.solution.enumVariant
                    == IntentTypesLib.SolutionTypeEnum.LaunchpadRemoveLiquidity
            ) {
                sendAck = true;
                // Execute Uniswap v3 remove liquidity directly
                uint256 actualAmount0;
                uint256 actualAmount1;
                (result, actualAmount0, actualAmount1) = executeUniswapV3RemoveLiquidity(
                    orderData.order, receiverAddress, amountIn, nativeTokenWrapperAddress
                );

                metadata.enumVariant = IntentTypesLib.IntentPayloadEnum.LaunchpadRemoveLiquidity;
                metadata.data = abi.encode(
                    IntentTypesLib.AcknowledgementMetadataLaunchpadRemoveLiquidity({
                        amount0: actualAmount0,
                        amount1: actualAmount1
                    })
                );

                if (!result) {
                    errorMessage = "Uniswap v3 remove liquidity failed";
                }
            } else {
                revert InvalidSolutionType();
            }
        } else if (order.enumVariant == IntentTypesLib.VaultBoundMessageEnum.CancelOrder) {
            // cancel order
        } else {
            revert InvalidOrderType();
        }
    }

    function depositStake(
        address stakingManagerCallAdaptor,
        address nativeTokenWrapperAddress,
        IntentTypesLib.VaultBoundMessagePlaceOrderData memory orderData,
        IntentTypesLib.SolutionTypeStakeData memory stakeData
    ) internal returns (address tokenReceivedAddress_, uint256 tokensReceived_) {
        address tokenIn = orderData.order.tokenIn.bytes32ToAddress();

        if (tokenIn == nativeTokenWrapperAddress) {
            // unwrap native tokens
            IWETH9(nativeTokenWrapperAddress).withdraw(orderData.order.amountIn);

            // stake native tokens
            (tokenReceivedAddress_, tokensReceived_) = StakingManager(
                payable(stakingManagerCallAdaptor)
            ).deposit{ value: orderData.order.amountIn }(
                orderData.order.amountIn,
                tokenIn,
                stakeData.stakeProtocol,
                stakeData.stakingPoolAddress.bytes32ToAddress()
            );
        } else {
            IERC20(tokenIn).approve(stakingManagerCallAdaptor, orderData.order.amountIn);
            (tokenReceivedAddress_, tokensReceived_) = StakingManager(
                payable(stakingManagerCallAdaptor)
            ).deposit(
                orderData.order.amountIn,
                tokenIn,
                stakeData.stakeProtocol,
                stakeData.stakingPoolAddress.bytes32ToAddress()
            );
        }
    }

    function storeOrderHash(
        IntentTypesLib.SolidityOrder memory orderData,
        bytes32 receiverAddress,
        uint256 amountIn,
        address mockedLNExternalCallAdaptor,
        address usdcTokenAddress,
        bytes memory debridgeMetadata
    ) internal {
        bytes32 tokenOut = orderData.tokenOut;
        if (debridgeMetadata.length > 0) {
            tokenOut = usdcTokenAddress.addressToBytes32();
        }
        IMockLN.MockLNOrder memory mockedLnOrder = IMockLN.MockLNOrder(
            orderData.orderId,
            orderData.tokenIn,
            tokenOut,
            amountIn,
            orderData.amountOut,
            orderData.destinationChainId,
            orderData.sourceChainId,
            receiverAddress,
            bytes32(0), // filler can be empty, as it's just resolved on-chain without any
            // solver
            false, // multiLeg is false, as it's just a direct swap
            bytes32(0), // stableCoinAddress is empty, as it's just a direct swap
            debridgeMetadata,
            orderData.timeoutUnixTimestampInSec
        );
        bytes32 orderHash = keccak256(abi.encode(mockedLnOrder));
        address tokenInAddress = mockedLnOrder.tokenIn.bytes32ToAddress();
        // Approve maximum tokens to the mockLN contract to avoid re-approval
        // every time the order is placed since mockLN contract is trusted by the
        // vault.
        checkAndApproveAllowance(
            tokenInAddress, mockedLNExternalCallAdaptor, mockedLnOrder.amountIn, type(uint256).max
        );
        IMockLN(mockedLNExternalCallAdaptor).storeOrderHash(mockedLnOrder.orderId, orderHash);
    }

    function checkAndApproveAllowance(
        address tokenInAddress,
        address spender,
        uint256 amount,
        uint256 newAllowance
    ) internal {
        uint256 existingAllowance = IERC20(tokenInAddress).allowance(address(this), spender);
        if (existingAllowance < amount) {
            safeApprove(IERC20(tokenInAddress), spender, newAllowance, existingAllowance);
        }
    }

    function transferErc20Tokens(
        address _nativeTokenWrapperAddress,
        address _tokenAddress,
        uint256 _tokenAmount,
        address _to
    ) internal {
        if (_nativeTokenWrapperAddress == _tokenAddress) {
            // if nativeTokenWrapper, unwrap and transfer native tokens
            IWETH9(_nativeTokenWrapperAddress).withdraw(_tokenAmount);
            payable(_to).transfer(_tokenAmount);
        } else {
            IERC20(_tokenAddress).safeTransfer(_to, _tokenAmount);
        }
    }

    // Using double approval to protect against double spend race condtion
    // which is known to happen in older erc20 tokens like USDT.
    //
    // It mimics the behavior of approve method which overrides the existing allowance
    // with the new amount.
    function safeApprove(
        IERC20 token,
        address spender,
        uint256 amount,
        uint256 currentAllowance
    ) internal {
        if (currentAllowance < amount) {
            token.safeIncreaseAllowance(spender, amount - currentAllowance);
        } else {
            token.safeDecreaseAllowance(spender, currentAllowance - amount);
        }
    }

    /**
     * @dev Executes a Uniswap v3 swap directly through the SwapRouter
     * @param order The order containing swap parameters
     * @param receiverAddress The address to receive the output tokens
     * @param amountIn The amount of input tokens to swap
     * @param nativeTokenWrapperAddress The WETH9 contract address
     * @return success Whether the swap was successful
     * @return amountOut The actual amount of output tokens received
     */
    function executeUniswapV3Swap(
        IntentTypesLib.SolidityOrder memory order,
        bytes32 receiverAddress,
        uint256 amountIn,
        address nativeTokenWrapperAddress
    ) internal returns (bool success, uint256 amountOut) {
        // Initialize return values
        success = false;
        amountOut = 0;

        // Get Uniswap router address from the calling vault
        // Since MessageHandler is a library, address(this) refers to the calling contract (Vault)
        address uniswapRouter = IVault(address(this)).uniswapV3SwapRouter();

        // Early return if router not set
        if (uniswapRouter == address(0)) {
            return (false, 0);
        }

        address tokenIn = order.tokenIn.bytes32ToAddress();
        address tokenOut = order.tokenOut.bytes32ToAddress();
        address receiver = receiverAddress.bytes32ToAddress();

        // Wrap the swap execution in try-catch to handle any failures gracefully
        try IERC20(tokenIn).allowance(address(this), uniswapRouter) returns (
            uint256 currentAllowance
        ) {
            // Check and approve allowance
            if (currentAllowance < amountIn) {
                try IERC20(tokenIn).approve(uniswapRouter, type(uint256).max) returns (
                    bool approveSuccess
                ) {
                    if (!approveSuccess) {
                        return (false, 0);
                    }
                } catch {
                    return (false, 0);
                }
            }

            // Prepare swap parameters - using 1% fee tier
            // For Vault receivers, send tokens to this contract first, then transfer properly
            address swapRecipient = order.receiver.enumVariant == IntentTypesLib.ReceiverEnum.Vault
                ? address(this)
                : receiver;

            ISwapRouter.ExactInputSingleParams memory params = ISwapRouter.ExactInputSingleParams({
                tokenIn: tokenIn,
                tokenOut: tokenOut,
                fee: 10000, // 1% fee tier
                recipient: swapRecipient,
                deadline: order.timeoutUnixTimestampInSec,
                amountIn: amountIn,
                amountOutMinimum: order.amountOut, // Use expected amountOut as minimum
                sqrtPriceLimitX96: 0 // No price limit
             });

            // Execute the swap with try-catch to handle slippage errors
            try ISwapRouter(uniswapRouter).exactInputSingle(params) returns (uint256 _amountOut) {
                amountOut = _amountOut;
                success = true;

                // Handle token transfer based on receiver type
                if (order.receiver.enumVariant == IntentTypesLib.ReceiverEnum.UserAddress) {
                    // For user addresses, use the existing transferErc20Tokens function for proper
                    // handling
                    transferErc20Tokens(nativeTokenWrapperAddress, tokenOut, amountOut, receiver);
                }
                // For Vault receivers, tokens are already in the vault (this contract), no
                // additional
                // transfer needed
            } catch {
                // Swap failed (likely due to slippage), return failure without reverting
                success = false;
                amountOut = 0;
            }
        } catch {
            // Failed to check allowance or other preliminary operations
            success = false;
            amountOut = 0;
        }
    }

    /**
     * @dev Executes a Uniswap v3 add liquidity operation directly through the
     * NonfungiblePositionManager
     * @param order The order containing liquidity parameters
     * @param receiverAddress The address to receive the NFT position
     * @param amountIn The amount of input tokens to use (for token0 or combined)
     * @param nativeTokenWrapperAddress The WETH9 contract address
     * @return success Whether the operation was successful
     * @return tokenId The NFT token ID of the created position
     * @return amount0 The actual amount of token0 used
     * @return amount1 The actual amount of token1 used
     */
    function executeUniswapV3AddLiquidity(
        IntentTypesLib.SolidityOrder memory order,
        bytes32 receiverAddress,
        uint256 amountIn,
        address nativeTokenWrapperAddress
    ) internal returns (bool success, uint256 tokenId, uint256 amount0, uint256 amount1) {
        // Initialize return values
        success = false;
        tokenId = 0;
        amount0 = 0;
        amount1 = 0;

        // Get Uniswap position manager address from the calling vault
        address positionManager = IVault(address(this)).uniswapV3PositionManager();

        // Early return if position manager not set
        if (positionManager == address(0)) {
            return (false, 0, 0, 0);
        }

        // Decode add liquidity data (if this fails, the function will revert, but that's expected
        // for malformed data)
        IntentTypesLib.SolutionTypeLaunchpadAddLiquidityData memory addLiquidityData =
            abi.decode(order.solution.data, (IntentTypesLib.SolutionTypeLaunchpadAddLiquidityData));

        address token0 = addLiquidityData.token0.bytes32ToAddress();
        address token1 = addLiquidityData.token1.bytes32ToAddress();
        address receiver = receiverAddress.bytes32ToAddress();

        // Try to approve tokens with error handling
        try IERC20(token0).allowance(address(this), positionManager) returns (uint256 allowance0) {
            if (allowance0 < addLiquidityData.amount0Max) {
                try IERC20(token0).approve(positionManager, type(uint256).max) returns (
                    bool success0
                ) {
                    if (!success0) {
                        return (false, 0, 0, 0);
                    }
                } catch {
                    return (false, 0, 0, 0);
                }
            }
        } catch {
            return (false, 0, 0, 0);
        }

        try IERC20(token1).allowance(address(this), positionManager) returns (uint256 allowance1) {
            if (allowance1 < addLiquidityData.amount1Max) {
                try IERC20(token1).approve(positionManager, type(uint256).max) returns (
                    bool success1
                ) {
                    if (!success1) {
                        return (false, 0, 0, 0);
                    }
                } catch {
                    return (false, 0, 0, 0);
                }
            }
        } catch {
            return (false, 0, 0, 0);
        }

        // Prepare mint parameters
        // For Vault receivers, send NFT to this contract first, then handle properly
        address nftRecipient = order.receiver.enumVariant == IntentTypesLib.ReceiverEnum.Vault
            ? address(this)
            : receiver;

        INonfungiblePositionManager.MintParams memory params = INonfungiblePositionManager
            .MintParams({
            token0: token0,
            token1: token1,
            fee: 10000, // 1% fee tier
            tickLower: int24(addLiquidityData.tickLower),
            tickUpper: int24(addLiquidityData.tickUpper),
            amount0Desired: addLiquidityData.amount0Max,
            amount1Desired: addLiquidityData.amount1Max,
            amount0Min: 0, // Allow any amount for flexibility
            amount1Min: 0, // Allow any amount for flexibility
            recipient: nftRecipient,
            deadline: order.timeoutUnixTimestampInSec
        });

        // Execute the liquidity addition with try-catch to handle failures
        try INonfungiblePositionManager(positionManager).mint(params) returns (
            uint256 _tokenId, uint128 liquidity, uint256 _amount0, uint256 _amount1
        ) {
            tokenId = _tokenId;
            amount0 = _amount0;
            amount1 = _amount1;
            success = true;

            // If successful, no additional transfer needed for vault receivers since NFT already
            // sent to correct recipient
            // For user addresses, NFT is already sent directly to the user
        } catch {
            // Liquidity addition failed, return failure without reverting
            success = false;
            tokenId = 0;
            amount0 = 0;
            amount1 = 0;
        }
    }

    /**
     * @dev Executes a Uniswap v3 remove liquidity operation directly through the
     * NonfungiblePositionManager
     * @param order The order containing liquidity removal parameters
     * @param receiverAddress The address to receive the tokens
     * @param amountIn Not used for remove liquidity, but kept for interface consistency
     * @param nativeTokenWrapperAddress The WETH9 contract address
     * @return success Whether the operation was successful
     * @return amount0 The amount of token0 collected
     * @return amount1 The amount of token1 collected
     */
    function executeUniswapV3RemoveLiquidity(
        IntentTypesLib.SolidityOrder memory order,
        bytes32 receiverAddress,
        uint256 amountIn,
        address nativeTokenWrapperAddress
    ) internal returns (bool success, uint256 amount0, uint256 amount1) {
        // Initialize return values
        success = false;
        amount0 = 0;
        amount1 = 0;

        // Get Uniswap position manager address from the calling vault
        address positionManager = IVault(address(this)).uniswapV3PositionManager();

        // Early return if position manager not set
        if (positionManager == address(0)) {
            return (false, 0, 0);
        }

        // Decode remove liquidity data (if this fails, the function will revert, but that's
        // expected for malformed data)
        IntentTypesLib.SolutionTypeLaunchpadRemoveLiquidityData memory removeLiquidityData = abi
            .decode(order.solution.data, (IntentTypesLib.SolutionTypeLaunchpadRemoveLiquidityData));

        address receiver = receiverAddress.bytes32ToAddress();
        uint256 tokenId = removeLiquidityData.tokenId;

        // Get position info with error handling
        try INonfungiblePositionManager(positionManager).positions(tokenId) returns (
            uint96,
            address,
            address token0,
            address token1,
            uint24,
            int24,
            int24,
            uint128 liquidity,
            uint256,
            uint256,
            uint128,
            uint128
        ) {
            // Decrease liquidity (remove all liquidity from the position)
            INonfungiblePositionManager.DecreaseLiquidityParams memory decreaseParams =
            INonfungiblePositionManager.DecreaseLiquidityParams({
                tokenId: tokenId,
                liquidity: liquidity, // Remove all liquidity
                amount0Min: removeLiquidityData.amount0Min,
                amount1Min: removeLiquidityData.amount1Min,
                deadline: order.timeoutUnixTimestampInSec
            });

            // Execute liquidity removal with try-catch
            try INonfungiblePositionManager(positionManager).decreaseLiquidity(decreaseParams)
            returns (uint256 _amount0, uint256 _amount1) {
                amount0 = _amount0;
                amount1 = _amount1;

                // Collect the tokens with error handling
                // For Vault receivers, collect to this contract first, then transfer properly
                address collectRecipient = order.receiver.enumVariant
                    == IntentTypesLib.ReceiverEnum.Vault ? address(this) : receiver;

                INonfungiblePositionManager.CollectParams memory collectParams =
                INonfungiblePositionManager.CollectParams({
                    tokenId: tokenId,
                    recipient: collectRecipient,
                    amount0Max: type(uint128).max, // Collect all available
                    amount1Max: type(uint128).max // Collect all available
                 });

                // Collect the removed liquidity tokens with try-catch
                try INonfungiblePositionManager(positionManager).collect(collectParams) {
                    // Handle token transfer based on receiver type (only for user addresses)
                    if (order.receiver.enumVariant == IntentTypesLib.ReceiverEnum.UserAddress) {
                        // For user addresses, use the existing transferErc20Tokens function for
                        // proper handling
                        transferErc20Tokens(nativeTokenWrapperAddress, token0, amount0, receiver);
                        transferErc20Tokens(nativeTokenWrapperAddress, token1, amount1, receiver);
                    }
                    // For Vault receivers, tokens are already in the vault (this contract), no
                    // additional
                    // transfer needed

                    success = true;
                } catch {
                    // Collect failed, return failure without reverting
                    success = false;
                    amount0 = 0;
                    amount1 = 0;
                }
            } catch {
                // Decrease liquidity failed, return failure without reverting
                success = false;
                amount0 = 0;
                amount1 = 0;
            }
        } catch {
            // Failed to get position info, return failure without reverting
            success = false;
            amount0 = 0;
            amount1 = 0;
        }
    }
}
