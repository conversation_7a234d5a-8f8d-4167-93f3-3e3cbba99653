// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

// MyOApp imports
import { Vault } from "../src/Vault.sol";

// OApp imports
import {
    EnforcedOptionParam,
    IOAppOptionsType3
} from "@layerzerolabs/oapp-evm/contracts/oapp/libs/OAppOptionsType3.sol";
import { OptionsBuilder } from "@layerzerolabs/oapp-evm/contracts/oapp/libs/OptionsBuilder.sol";

// OZ imports

import { IntentProcessorLib } from "../src/libraries/IntentProcessorLib.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";

// Forge imports

import "forge-std/Test.sol";
import "forge-std/console.sol";

// DevTools imports
import { TestHelperOz5 } from "@layerzerolabs/test-devtools-evm-foundry/contracts/TestHelperOz5.sol";

import { DeployIntentProcessorV2 } from "../script/DeployIntentProcessorV2.s.sol";

import { BalanceManager } from "../src/BalanceManager.sol";

import { IntentProcessorV2 } from "../src/IntentProcessorV2.sol";
import { MockLN } from "../src/MockLN.sol";
import { StakingManager } from "../src/StakingManager.sol";

import { WETH9 } from "@aave/dependencies/weth/WETH9.sol";
import { Options } from "openzeppelin-foundry-upgrades/Options.sol";
import { Upgrades } from "openzeppelin-foundry-upgrades/Upgrades.sol";

import { DeployMockLN } from "../script/DeployMockLN.s.sol";
import { DeployStakingManager } from "../script/DeployStakingManager.s.sol";
import { IntentLibV2 } from "../src/libraries/IntentLibV2.sol";
import { IntegrationTestIntentProcessorV2 } from "../src/mock/IntegrationTestIntentProcessorV2.sol";

import { AuctionManager } from "../src/AuctionManager.sol";

import { UpgradeStakingManager } from "../script/UpgradeStakingManager.s.sol";
import { UpgradeVault } from "../script/UpgradeVault.s.sol";

import { Token } from "../src/Token.sol";
import { IntentTypesLib } from "../src/libraries/IntentTypesLib.sol";

import { Deposit } from "../src/Deposit.sol";

import { DepositFactory } from "../src/DepositFactory.sol";
import { IMailbox } from "../src/interfaces/IMailbox.sol";
import { IMockLN } from "../src/interfaces/IMockLN.sol";
import { IntentLib } from "../src/libraries/IntentLib.sol";
import { MessageHandler } from "../src/libraries/MessageHandler.sol";

contract IntegrationTestVault is Vault {
    using IntentLib for address;
    using IntentLib for bytes32;
    using IntentLib for bytes;
    using MessageHandler for bytes;
    /// @custom:oz-upgrades-unsafe-allow constructor

    uint32 public immutable chainid;

    // the acknowledgements are stored so that it can be sent in
    // another transaction
    mapping(uint64 => bytes) public pendingAcknowledgments;

    constructor(
        address lzEndpoint,
        address owner,
        address hyperlaneMailbox,
        address mockLNProxy,
        uint32 lzEid,
        uint32 hostChainid,
        address stakingManagerAddress,
        address nativeTokenWrapperAddress,
        address usdcTokenAddress
    ) Vault(lzEndpoint) {
        chainid = hostChainid;
        Vault.initialize(
            owner,
            lzEndpoint,
            hyperlaneMailbox, //hypMailbox for testnets on which the deployment is on
            0xeF4fB24aD0916217251F553c0596F8Edc630EB66, //deBridgeDLNSource
            0xE7351Fd770A37282b91D153Ee690B63579D6dd7f, // deBridgeDLNDestination
            ******************************************, // DlnExternalCallAdapter
            mockLNProxy, // mockln
            0x4a12453F44F9d415FAAf4b3677707D4dc8Ce53D8, // Intent processor address
            stakingManagerAddress,
            lzEid,
            nativeTokenWrapperAddress,
            usdcTokenAddress
        );
    }

    function sendDepositMessage(
        address userAddress,
        uint256 amount,
        address erc20TokenAddress,
        IntentTypesLib.InteropProvider provider,
        uint128 gasLimit,
        uint128 value
    ) internal override {
        IntentTypesLib.SolidityIntentProcessorBoundMessage memory depositMessage;
        depositMessage.enumVariant = IntentTypesLib.IntentProcessorBoundMessageEnum.Deposit;
        depositMessage.data = abi.encode(
            IntentTypesLib.IntentProcessorBoundMessageDepositData(
                userAddress, amount, erc20TokenAddress.addressToBytes32(), chainid
            )
        );

        if (provider == IntentTypesLib.InteropProvider.LayerZero) {
            _lzSendMessage(intentProcessorLZChainId, abi.encode(depositMessage), gasLimit, value);
        } else if (provider == IntentTypesLib.InteropProvider.Hyperlane) {
            /// @aditya172926 I guess we need to store the destination domain
            /// for hyperlane.
            // (abi.encode(depositMessage))._hypSendMessage(intentProcessorAddress,
            // intentProcessorLZChainId, hypMailbox);
            hypSendMessage(
                intentProcessorAddress, intentProcessorLZChainId, abi.encode(depositMessage)
            );
        } else {
            revert IntentLib.InvalidInteropProvider();
        }

        emit IntentLib.DepositedFunds(msg.sender, erc20TokenAddress, amount);
    }

    function routeMessage(
        bytes memory _message,
        IntentTypesLib.InteropProvider provider
    ) internal override {
        console.log("Balance", address(this).balance);
        uint256 gasLeftBefore = gasleft();
        (
            bool sendAck,
            uint64 orderId,
            bool result,
            string memory errorMessage,
            IntentTypesLib.SolidityAcknowledgementMetadata memory metadata
        ) = _message.route(
            deBridgeDLNSource,
            mockedLNExternalCallAdaptor,
            stakingManagerCallAdaptor,
            provider,
            nativeTokenWrapperAddress,
            address(0)
        );
        uint256 gasLeftAfter = gasleft();
        console.log("Gas used for routeMessage: ", gasLeftBefore - gasLeftAfter);
        console.log("result: ", result);
        console.log("errorMessage: ", errorMessage);
        IntentTypesLib.SolidityIntentProcessorBoundMessage memory ackMessage;
        ackMessage.enumVariant = IntentTypesLib.IntentProcessorBoundMessageEnum.Acknowledgement;
        ackMessage.data = abi.encode(
            IntentTypesLib.IntentProcessorBoundMessageAcknowledgementData(
                orderId, result, errorMessage, metadata
            )
        );
        pendingAcknowledgments[orderId] = abi.encode(ackMessage);
    }

    // Internal helper function to handle both providers
    function _sendMessage(
        IntentTypesLib.InteropProvider provider,
        uint32 destinationId,
        bytes memory message,
        uint128 gasLimit,
        uint128 value
    ) internal {
        if (provider == IntentTypesLib.InteropProvider.LayerZero) {
            _lzSendMessage(destinationId, message, gasLimit, value);
        } else if (provider == IntentTypesLib.InteropProvider.Hyperlane) {
            //message._hypSendMessage(intentProcessorAddress, destinationId, hypMailbox);

            hypSendMessage(intentProcessorAddress, destinationId, message);
        } else {
            revert IntentLib.InvalidInteropProvider();
        }
    }

    function sendPendingAcknowledgment(
        uint64 intentId,
        uint128 gasLimit,
        uint128 value
    ) public payable {
        _lzSendMessage(intentProcessorLZChainId, pendingAcknowledgments[intentId], gasLimit, value);
        delete pendingAcknowledgments[intentId];
    }

    function getPendingAcknowledgment(
        uint64 intentId
    ) public view returns (bytes memory) {
        return pendingAcknowledgments[intentId];
    }
}

contract IntegrationTestMockLN is MockLN {
    constructor(
        address _lzEndpointAddress
    ) MockLN(_lzEndpointAddress) {
        MockLN.initialize(msg.sender, _lzEndpointAddress, address(0));
    }
}

contract MyOAppTest is TestHelperOz5 {
    using OptionsBuilder for bytes;

    using IntentLib for bytes32;
    using IntentLib for address;

    uint32 private aEid = 1; // For Vault A
    uint32 private bEid = 2; // For Vault B
    uint32 private cEid = 3; // For Intent Processor

    uint256 private aVaultChainId = 100; // For Vault A
    uint256 private bVaultChainId = 200; // For Vault B

    IntegrationTestIntentProcessorV2 private intentProcessor;

    IntegrationTestVault private aVault;
    IntegrationTestVault private bVault;

    DepositFactory public adepositFactory;
    DepositFactory public bdepositFactory;

    IntegrationTestMockLN private aMockLN;
    IntegrationTestMockLN private bMockLN;

    StakingManager private aStakingManager;
    StakingManager private bStakingManager;

    AuctionManager private auctionManager;

    address private userA = address(0x1);
    address private userB = address(0x2);
    // This is the address to which transfers would be sent.
    //
    // This address is not part of the vault and is an EOA and will
    // have 0 balance unless transferred through the vaults.
    address private userC = address(0x3);

    Token private token1;
    Token private token2;

    Token private stableCoin1;
    Token private stableCoin2;

    address private vaultOwner = ******************************************;
    address private stakingManagerAddress = ******************************************;

    address private stakingUser = ******************************************;
    address private usdcTokenAddress = address(2);

    address private hyperlaneMailbox = address(0x0);
    uint256 private initialBalance = 100 ether;

    function setUp() public virtual override {
        // connect vm to local existing anvil node
        vm.deal(userA, 1000 ether);
        vm.deal(userB, 1000 ether);
        vm.deal(vaultOwner, 1000 ether);

        super.setUp();

        setUpEndpoints(3, LibraryType.UltraLightNode);
        console.log("endpoints[aEid]", address(endpoints[aEid]));

        aMockLN = new IntegrationTestMockLN(address(endpoints[aEid]));

        WETH9 wrapperTokenAddress = new WETH9();

        aVault = new IntegrationTestVault(
            address(endpoints[aEid]),
            vaultOwner,
            hyperlaneMailbox,
            address(aMockLN),
            aEid,
            uint32(aVaultChainId),
            stakingManagerAddress,
            address(wrapperTokenAddress),
            usdcTokenAddress
        );

        adepositFactory = new DepositFactory(vaultOwner);
        aVault.setDepositFactoryAddress(address(adepositFactory));

        vm.prank(vaultOwner);
        aMockLN.setVaultAddress(address(aVault));
        aVault.changeRollupLzChainId(cEid);

        bMockLN = new IntegrationTestMockLN(address(endpoints[bEid]));

        bVault = new IntegrationTestVault(
            address(endpoints[bEid]),
            vaultOwner,
            hyperlaneMailbox,
            address(bMockLN),
            bEid,
            uint32(bVaultChainId),
            stakingManagerAddress,
            address(wrapperTokenAddress),
            usdcTokenAddress
        );

        vm.prank(vaultOwner);
        bMockLN.setVaultAddress(address(bVault));
        bVault.changeRollupLzChainId(cEid);

        // Deploy StakingManager contract
        DeployStakingManager deployStakingManager = new DeployStakingManager();
        address lidoWithdrawalQueue = ******************************************;
        address etherFiWithdrawalRequestNFTAddress = ******************************************;
        address newStakingManagerAddress = deployStakingManager.run(
            vaultOwner, address(aVault), lidoWithdrawalQueue, etherFiWithdrawalRequestNFTAddress
        );
        aStakingManager = StakingManager(payable(newStakingManagerAddress));
        console.log("StakingManager address: ", newStakingManagerAddress);
        vm.makePersistent(newStakingManagerAddress);

        vm.prank(vaultOwner);
        aVault.updateStakingManagerCallAdaptor(newStakingManagerAddress);

        // Deploy Balance Manager
        vm.prank(vaultOwner);
        BalanceManager balanceManager = new BalanceManager();
        balanceManager.initialize(vaultOwner);

        address balanceManagerAddress = address(balanceManager);

        // Deploy Intent Processor
        vm.prank(vaultOwner);
        intentProcessor = new IntegrationTestIntentProcessorV2(address(endpoints[cEid]));

        IntentLibV2.IntentProcessorInitArgs memory args;
        args.owner = vaultOwner;
        args.lzEndpointAddress = address(endpoints[cEid]);
        args.balanceManagerAddress = balanceManagerAddress;
        args.hyperlaneMailboxAddress = address(0x4);
        args.trustedForwarderAddress = address(0x5);
        args.feeCollector = vaultOwner;
        // args.feeBasisPoints = 1;

        intentProcessor.initialize(args);
        address intentProcessorAddress = address(intentProcessor);
        balanceManager.addBalanceUpdater(intentProcessorAddress);

        // Deploy AuctionManager
        vm.prank(vaultOwner);
        auctionManager = new AuctionManager();
        auctionManager.initialize(vaultOwner, intentProcessorAddress);

        intentProcessor.setAuctioneer(address(auctionManager));

        address[] memory oapps = new address[](3);
        oapps[0] = address(aVault);
        oapps[1] = address(bVault);
        oapps[2] = intentProcessorAddress;
        this.wireOApps(oapps);

        aMockLN.setPeer(bEid, address(bMockLN).addressToBytes32());
        bMockLN.setPeer(aEid, address(aMockLN).addressToBytes32());

        // Deploy some tokens
        token1 = new Token(userA, 10000, "TestToken1", "TTK1");
        token1.transfer(userA, 2000000000000000000000);
        token1.transfer(vaultOwner, 5000000000000000000000);

        token2 = new Token(userB, 10000, "TestToken2", "TTK2");
        token2.transfer(userB, 2000000000000000000000);
        token2.transfer(userC, 2000000000000000000000);
        token2.transfer(vaultOwner, 5000000000000000000000);

        // Deploy stable coins
        stableCoin1 = new Token(userC, 10000, "StableCoin1", "SC1");
        stableCoin1.transfer(userB, 2000000000000000000000);
        stableCoin1.transfer(vaultOwner, 5000000000000000000000);

        stableCoin2 = new Token(userB, 10000, "StableCoin2", "SC2");
        stableCoin2.transfer(userB, 2000000000000000000000);
        stableCoin2.transfer(vaultOwner, 5000000000000000000000);
    }

    function _createMockLNOrderSource(
        uint64 orderId,
        bytes32 tokenIn,
        bytes32 tokenOut,
        uint256 amountIn,
        uint256 amountOut,
        IntentTypesLib.LiquidityNetworkMockedLNData memory mockedLnData,
        bytes32 receiverAddress,
        bool multiLeg,
        uint64 timeoutUnixTimestampInSec
    ) internal pure returns (IMockLN.MockLNOrder memory) {
        return IMockLN.MockLNOrder(
            orderId,
            tokenIn,
            tokenOut,
            amountIn,
            amountOut,
            mockedLnData.destinationChainId,
            mockedLnData.sourceChainId,
            receiverAddress,
            mockedLnData.filler,
            multiLeg,
            mockedLnData.stableCoinAddress,
            bytes(""),
            timeoutUnixTimestampInSec
        );
    }

    function _createMockLNOrderDestination(
        uint64 orderId,
        bytes32 tokenIn,
        bytes32 tokenOut,
        uint256 amountIn,
        uint256 amountOut,
        IntentTypesLib.LiquidityNetworkMockedLNData memory mockedLnData,
        bytes32 receiverAddress,
        bool multiLeg,
        uint64 timeoutUnixTimestampInSec
    ) internal pure returns (IMockLN.MockLNOrder memory) {
        return IMockLN.MockLNOrder(
            orderId,
            tokenIn,
            tokenOut,
            amountIn,
            amountOut,
            mockedLnData.destinationChainId,
            mockedLnData.sourceChainId,
            receiverAddress,
            mockedLnData.filler,
            multiLeg,
            mockedLnData.stableCoinAddress,
            bytes(""),
            timeoutUnixTimestampInSec
        );
    }

    function _createSimpleMockLNOrder(
        uint64 orderId,
        bytes32 tokenIn,
        bytes32 tokenOut,
        uint256 amountIn,
        uint256 amountOut,
        uint256 destinationChainId,
        uint256 sourceChainId,
        bytes32 receiverAddress,
        uint64 timeoutUnixTimestampInSec
    ) internal pure returns (IMockLN.MockLNOrder memory) {
        return IMockLN.MockLNOrder(
            orderId,
            tokenIn,
            tokenOut,
            amountIn,
            amountOut,
            destinationChainId,
            sourceChainId,
            receiverAddress,
            bytes32(0),
            false,
            bytes32(0),
            bytes(""),
            timeoutUnixTimestampInSec
        );
    }

    function test_constructor() public view {
        assertEq(aVault.owner(), vaultOwner);
        assertEq(bVault.owner(), vaultOwner);
        assertEq(intentProcessor.owner(), vaultOwner);

        assertEq(address(aVault.endpoint()), endpoints[aEid]);
        assertEq(address(bVault.endpoint()), endpoints[bEid]);
        assertEq(address(intentProcessor.endpoint()), endpoints[cEid]);
    }

    function test_cross_chain_transfer_auction() public {
        // deposit funds
        // register vaults
        // submit intent
        // push orders ( bid and ask)
        // choose winner from auction
        // choose winner from intent processor
        // fulfill on source (sends ack)
        // fulfill on destination (sends ack)
        // claim on source
        // claim on destination
        // verify ack
        // verify balances
        console.log("aVault", address(aVault));
        console.log("bVault", address(bVault));
        vm.startPrank(vaultOwner);

        uint256 amountIn = 10 * 10 ** 18;
        uint256 amountOut = 9 * 10 ** 18;
        uint32 sourceChainId = uint32(aVaultChainId);
        uint32 destinationChainId = uint32(bVaultChainId);

        depositFunds(userA, address(token1), amountIn);
        registerVaults();

        IntentTypesLib.SolidityIntentPayload memory intent = IntentTypesLib.SolidityIntentPayload({
            enumVariant: IntentTypesLib.IntentPayloadEnum.Transact,
            data: abi.encode(
                IntentTypesLib.IntentPayloadTransactData({
                    tokenIn: address(token1).addressToBytes32(),
                    amountIn: amountIn,
                    tokenOut: address(token2).addressToBytes32(),
                    amountOut: amountOut,
                    destination: IntentTypesLib.SolidityTransactDestination({
                        enumVariant: IntentTypesLib.TransactDestinationEnum.DestinationVault,
                        data: abi.encode(userA)
                    }),
                    timeoutTimestampInSeconds: uint64(block.timestamp + 6 hours),
                    sourceChainId: sourceChainId,
                    destinationChainId: destinationChainId
                })
            )
        });

        uint64 intentId = intentProcessor.intentNonce();
        vm.stopPrank();
        vm.prank(userA);
        intentProcessor.submitIntent(intent);

        vm.startPrank(vaultOwner);

        uint256 price = 100;
        uint256 amountInStableCoin = amountIn * price;
        AuctionManager.Order memory buyAuctionOrder = AuctionManager.Order({
            orderId: 1,
            intentId: intentId,
            quantity: amountInStableCoin,
            side: true,
            solver: userB.addressToBytes32()
        });

        AuctionManager.Order memory sellAuctionOrder = AuctionManager.Order({
            orderId: 2,
            intentId: intentId,
            quantity: amountOut,
            side: false,
            solver: userC.addressToBytes32()
        });
        auctionManager.addStableCoins(
            uint32(aVaultChainId), address(stableCoin1).addressToBytes32()
        );
        auctionManager.addStableCoins(
            uint32(bVaultChainId), address(stableCoin2).addressToBytes32()
        );

        auctionManager.push(buyAuctionOrder);
        auctionManager.push(sellAuctionOrder);

        auctionManager.chooseWinner(intentId, IntentTypesLib.InteropProvider.LayerZero, 0);

        intentProcessor.chooseWinner{ value: 0.01 ether }(
            intentId, IntentTypesLib.InteropProvider.LayerZero
        );

        (,,, uint256 rawIntent_feesTaken,) = intentProcessor.intents(intentId);

        (,, uint256 orderA_amountIn,, uint256 orderA_amountOut,,,,,,,,) = intentProcessor.orders(1);
        assertEq(orderA_amountIn, amountIn - rawIntent_feesTaken);
        assertEq(orderA_amountOut, amountIn * price);
        // IntentTypesLib.SolidityOrder memory orderB = intentProcessor.orders(2);
        (,,,, uint256 orderB_amountOut,,,,,,,,) = intentProcessor.orders(2);
        assertEq(orderB_amountOut, amountOut);

        uint256 gasLeftBefore = gasleft();
        verifyPackets(aEid, address(aVault));
        uint256 gasLeftAfter = gasleft();
        console.log("Gas used for verifyPackets: ", gasLeftBefore - gasLeftAfter);
        verifyPackets(bEid, address(bVault));
        uint256 gasLeftAfter2 = gasleft();
        console.log("Gas used for verifyPackets: ", gasLeftAfter - gasLeftAfter2);

        // Build the mockLN order
        (
            uint64 src_intentId,
            uint64 src_orderId,
            uint256 src_amountIn,
            bytes32 src_tokenIn,
            uint256 src_amountOut,
            bytes32 src_tokenOut,
            IntentTypesLib.SolidityReceiver memory src_receiver,
            uint256 src_sourceChainId,
            uint256 src_destinationChainId,
            uint64 src_timeoutUnixTimestampInSec,
            IntentTypesLib.SoliditySolutionType memory src_solution,
            address src_initiatorAddress,
            bool src_multiLeg
        ) = intentProcessor.orders(1);

        IntentTypesLib.SolidityLiquidityNetwork memory src_liquidityNetwork = abi.decode(
            src_solution.data, (IntentTypesLib.SolutionTypeCrossChainData)
        ).liquidityNetwork;
        IntentTypesLib.LiquidityNetworkMockedLNData memory src_mockedLnData =
            abi.decode(src_liquidityNetwork.data, (IntentTypesLib.LiquidityNetworkMockedLNData));
        bytes32 src_receiverAddress = IntentProcessorLib._getReceiverAddress(src_receiver);
        IMockLN.MockLNOrder memory mockedLnOrderSource = _createMockLNOrderSource(
            src_orderId,
            src_tokenIn,
            src_tokenOut,
            src_amountIn,
            src_amountOut,
            src_mockedLnData,
            src_receiverAddress,
            src_multiLeg,
            src_timeoutUnixTimestampInSec
        );
        // ===================================

        (
            uint64 dest_intentId,
            uint64 dest_orderId,
            uint256 dest_amountIn,
            bytes32 dest_tokenIn,
            uint256 dest_amountOut,
            bytes32 dest_tokenOut,
            IntentTypesLib.SolidityReceiver memory dest_receiver,
            uint256 dest_sourceChainId,
            uint256 dest_destinationChainId,
            uint64 dest_timeoutUnixTimestampInSec,
            IntentTypesLib.SoliditySolutionType memory dest_solution,
            address dest_initiatorAddress,
            bool dest_multiLeg
        ) = intentProcessor.orders(2);

        IntentTypesLib.SolidityLiquidityNetwork memory dest_liquidityNetwork = abi.decode(
            dest_solution.data, (IntentTypesLib.SolutionTypeCrossChainData)
        ).liquidityNetwork;
        IntentTypesLib.LiquidityNetworkMockedLNData memory dest_mockedLnData =
            abi.decode(dest_liquidityNetwork.data, (IntentTypesLib.LiquidityNetworkMockedLNData));
        bytes32 dest_receiverAddress = IntentProcessorLib._getReceiverAddress(dest_receiver);

        IMockLN.MockLNOrder memory mockedLnOrderDestination = _createMockLNOrderDestination(
            dest_orderId,
            dest_tokenIn,
            dest_tokenOut,
            dest_amountIn,
            dest_amountOut,
            dest_mockedLnData,
            dest_receiverAddress,
            dest_multiLeg,
            dest_timeoutUnixTimestampInSec
        );

        vm.stopPrank();
        vm.prank(userB);
        stableCoin1.approve(address(aMockLN), 10 ** 24);
        vm.prank(userB);
        gasLeftBefore = gasleft();
        aMockLN.fulfillOrderWithOrderData{ value: 0.01 ether }(
            1,
            mockedLnOrderSource,
            IntentTypesLib.InteropProvider.LayerZero,
            userB.addressToBytes32()
        );
        gasLeftAfter = gasleft();
        console.log("Gas used for fulfillOrderWithOrderData: ", gasLeftBefore - gasLeftAfter);
        vm.prank(userC);
        token2.approve(address(bMockLN), 10 ** 44);
        vm.prank(userC);
        vm.deal(userC, 10 ether);
        gasLeftBefore = gasleft();
        bMockLN.fulfillOrderWithOrderData{ value: 0.01 ether }(
            2,
            mockedLnOrderDestination,
            IntentTypesLib.InteropProvider.LayerZero,
            userC.addressToBytes32()
        );
        gasLeftAfter = gasleft();
        console.log("Gas used for fulfillOrderWithOrderData: ", gasLeftBefore - gasLeftAfter);
        verifyPackets(aEid, address(aMockLN));
        verifyPackets(cEid, address(intentProcessor));
        // Check if the user received the tokens
        assertEq(stableCoin1.balanceOf(userC), amountInStableCoin);

        // Check if the stablecoin balance is correct.
        // Check if user has the correct amount of token2
        // Check if fee collector has the right amount of fees
        BalanceManager balanceManager = BalanceManager(intentProcessor.balanceManagerAddress());
        (uint256 chainIdentifier1, bytes32 tokenAddress1, uint256 amount1) =
            balanceManager.userBalances(userA, 0);
        (uint256 chainIdentifier2, bytes32 tokenAddress2, uint256 amount2) =
            balanceManager.userBalances(userA, 1);
        (uint256 chainIdentifier3, bytes32 tokenAddress3, uint256 amount3) =
            balanceManager.userBalances(vaultOwner, 0);

        assertEq(chainIdentifier1, uint32(aVaultChainId));
        assertEq(tokenAddress1, address(token1).addressToBytes32());
        assertEq(amount1, 0);
        assertEq(chainIdentifier2, uint32(bVaultChainId));
        assertEq(tokenAddress2, address(token2).addressToBytes32());
        assertEq(amount2, amountOut);
        assertEq(chainIdentifier3, uint32(aVaultChainId));
        assertEq(tokenAddress3, address(token1).addressToBytes32());
        assertEq(amount3, rawIntent_feesTaken, "Fees taken does not match");
    }

    function test_same_chain_swap() public {
        // deposit funds
        // register vaults
        // submit intent
        // submit winning solution
        // choose winner from intent processor
        // fulfill on source (sends ack)
        // fulfill on destination (sends ack)
        // claim on source
        // claim on destination
        // verify ack
        // verify balances
        console.log("aVault", address(aVault));
        console.log("aMockLN", address(aMockLN));
        address oneInchRouter = 0x111111125421cA6dc452d289314280a0f8842A65;

        vm.startPrank(vaultOwner);

        uint256 amountIn = 10 * 10 ** 18;
        uint256 amountOut = 9 * 10 ** 18;
        bytes memory returnAmountData = abi.encode(amountOut);

        uint32 sourceChainId = uint32(aVaultChainId);

        depositFunds(userA, address(token1), amountIn);
        registerVaults();

        IntentTypesLib.SolidityIntentPayload memory intent = IntentTypesLib.SolidityIntentPayload({
            enumVariant: IntentTypesLib.IntentPayloadEnum.Transact,
            data: abi.encode(
                IntentTypesLib.IntentPayloadTransactData({
                    tokenIn: address(token1).addressToBytes32(),
                    amountIn: amountIn,
                    tokenOut: address(token2).addressToBytes32(),
                    amountOut: amountOut,
                    destination: IntentTypesLib.SolidityTransactDestination({
                        enumVariant: IntentTypesLib.TransactDestinationEnum.DestinationVault,
                        data: abi.encode(userA)
                    }),
                    timeoutTimestampInSeconds: uint64(block.timestamp + 6 hours),
                    sourceChainId: sourceChainId,
                    destinationChainId: sourceChainId
                })
            )
        });

        uint64 intentId = intentProcessor.intentNonce();
        vm.stopPrank();
        vm.prank(userA);
        intentProcessor.submitIntent(intent);

        vm.startPrank(vaultOwner);
        intentProcessor.chooseWinner{ value: 0.01 ether }(
            intentId, IntentTypesLib.InteropProvider.LayerZero
        );
        (,,, uint256 rawIntent_feesTaken,) = intentProcessor.intents(intentId);
        verifyPackets(aEid, address(aVault));

        // Build the mockLN order

        (
            uint64 src_intentId,
            uint64 src_orderId,
            uint256 src_amountIn,
            bytes32 src_tokenIn,
            uint256 src_amountOut,
            bytes32 src_tokenOut,
            IntentTypesLib.SolidityReceiver memory src_receiver,
            uint256 src_sourceChainId,
            uint256 src_destinationChainId,
            uint64 src_timeoutUnixTimestampInSec,
            IntentTypesLib.SoliditySolutionType memory src_solution,
            address src_initiatorAddress,
            bool src_multiLeg
        ) = intentProcessor.orders(1);
        console.logBool(src_multiLeg);

        bytes32 src_receiverAddress = IntentProcessorLib._getReceiverAddress(src_receiver);
        IMockLN.MockLNOrder memory mockedLnOrderSource = _createSimpleMockLNOrder(
            src_orderId,
            src_tokenIn,
            src_tokenOut,
            src_amountIn,
            src_amountOut,
            src_destinationChainId,
            src_sourceChainId,
            src_receiverAddress,
            src_timeoutUnixTimestampInSec
        );

        vm.stopPrank();
        vm.prank(userB);
        token2.approve(address(aMockLN), 10 ** 24);
        vm.prank(userB);
        uint256 gasLeftBefore = gasleft();

        vm.mockCall(
            oneInchRouter,
            abi.encodeWithSelector(bytes4(keccak256("call(bytes)"))),
            returnAmountData
        );

        token2.mint(address(aMockLN), amountOut);
        aMockLN.fulfillOrderWithOneInch{ value: 0.01 ether }(
            1,
            mockedLnOrderSource,
            IntentTypesLib.InteropProvider.LayerZero,
            abi.encodeWithSelector(bytes4(keccak256("call(bytes)")))
        );
        uint256 gasLeftAfter = gasleft();
        console.log("Gas used for fulfillOrderWithOneInch: ", gasLeftBefore - gasLeftAfter);
        verifyPackets(cEid, address(intentProcessor));

        // Validate balances

        // Check if the stablecoin balance is correct.
        // Check if user has the correct amount of token2 100000000000000000
        // Check if fee collector has the right amount of fees
        BalanceManager balanceManager = BalanceManager(intentProcessor.balanceManagerAddress());
        (uint256 chainIdentifier1, bytes32 tokenAddress1, uint256 amount1) =
            balanceManager.userBalances(userA, 0);
        (uint256 chainIdentifier2, bytes32 tokenAddress2, uint256 amount2) =
            balanceManager.userBalances(userA, 1);
        (uint256 chainIdentifier3, bytes32 tokenAddress3, uint256 amount3) =
            balanceManager.userBalances(vaultOwner, 0);
        assertEq(chainIdentifier1, uint32(aVaultChainId));
        assertEq(tokenAddress1, address(token1).addressToBytes32());
        assertEq(amount1, 0);
        assertEq(chainIdentifier2, uint32(aVaultChainId));
        assertEq(tokenAddress2, address(token2).addressToBytes32());
        assertEq(amount2, amountOut);
        assertEq(chainIdentifier3, uint32(aVaultChainId));
        assertEq(tokenAddress3, address(token1).addressToBytes32());
        assertEq(amount3, rawIntent_feesTaken);
    }

    /* 
      Helper functions
    */

    function validateBalances(
        IntentTypesLib.SolidityRawIntent memory intent
    ) internal view {
        if (intent.intentPayload.enumVariant == IntentTypesLib.IntentPayloadEnum.Transact) {
            IntentTypesLib.IntentPayloadTransactData memory transactData =
                abi.decode(intent.intentPayload.data, (IntentTypesLib.IntentPayloadTransactData));
            if (
                transactData.destination.enumVariant
                    == IntentTypesLib.TransactDestinationEnum.DestinationAddress
            ) {
                address destinationAddress = abi.decode(transactData.destination.data, (address));
                BalanceManager balanceManager =
                    BalanceManager(intentProcessor.balanceManagerAddress());
                (uint256 chainIdentifier1, bytes32 tokenAddress1, uint256 amount1) =
                    balanceManager.userBalances(userA, 0);

                console.log("chainIdentifier1: ", chainIdentifier1);
                console.logBytes32(tokenAddress1);
                console.log("amount1: ", amount1);

                assertEq(chainIdentifier1, transactData.sourceChainId);
                assertEq(tokenAddress1, transactData.tokenIn);
                assertEq(amount1, 0);

                // Token tokenOut = Token(transactData.tokenOut.bytes32ToAddress());
                // assertEq(tokenOut.balanceOf(destinationAddress), transactData.amountOut);
            } else if (
                transactData.destination.enumVariant
                    == IntentTypesLib.TransactDestinationEnum.DestinationVault
            ) {
                BalanceManager balanceManager =
                    BalanceManager(intentProcessor.balanceManagerAddress());
                (uint256 chainIdentifier1, bytes32 tokenAddress1, uint256 amount1) =
                    balanceManager.userBalances(userA, 0);
                (uint256 chainIdentifier2, bytes32 tokenAddress2, uint256 amount2) =
                    balanceManager.userBalances(userA, 1);

                assertEq(chainIdentifier1, transactData.sourceChainId);
                assertEq(chainIdentifier2, transactData.destinationChainId);
                assertEq(tokenAddress1, transactData.tokenIn);
                assertEq(tokenAddress2, transactData.tokenOut);
                assertEq(amount1, 0);
                assertEq(amount2, transactData.amountOut);
            } else {
                revert("Invalid transfer destination");
            }
        } else {
            revert("Invalid intent type");
        }
    }

    function depositFunds(address user, address tokenAddress, uint256 amount) internal {
        address depositContract = adepositFactory.deployDepositContract(user, address(aVault));
        Token token = Token(tokenAddress);
        token.transfer(depositContract, amount);
        aVault.depositFundsFromSmartAccount{ value: 0.001 ether }(
            depositContract, tokenAddress, IntentTypesLib.InteropProvider.LayerZero, 1_000_000, 0
        );
        verifyPackets(cEid, address(intentProcessor));
    }

    function registerVaults() internal {
        IntentTypesLib.Chain[] memory chains = new IntentTypesLib.Chain[](2);
        chains[0] = buildChainStructForVault(aVault);
        chains[1] = buildChainStructForVault(bVault);
        intentProcessor.registerVaults(chains[0]);
        intentProcessor.registerVaults(chains[1]);
    }

    function buildChainStructForVault(
        IntegrationTestVault _vault
    ) internal view returns (IntentTypesLib.Chain memory chain_) {
        IntentTypesLib.DomainId[] memory chainIds = new IntentTypesLib.DomainId[](2);
        chainIds[0] = IntentTypesLib.DomainId(
            uint256(_vault.lzSourceChainId()), IntentTypesLib.InteropProvider.LayerZero
        );

        IMailbox hyperLaneMailbox = _vault.hypMailbox();
        if (address(hyperLaneMailbox) != address(0)) {
            chainIds[1] = IntentTypesLib.DomainId(
                uint256(hyperLaneMailbox.localDomain()), IntentTypesLib.InteropProvider.Hyperlane
            );
        }

        chain_.domainIds = chainIds;
        chain_.identifier = _vault.chainid();
        chain_.vaultAddress = address(_vault).addressToBytes32();
        chain_.vaultAuthority = _vault.owner().addressToBytes32();
        chain_.poolAddress = address(_vault).addressToBytes32();
        chain_.isActive = true;
        chain_.executionEnvironment = IntentTypesLib.ExecutionEnvironment.EVM;
    }

    function buildSolution(
        IntentTypesLib.SolidityRawIntent memory intent
    ) public view returns (IntentTypesLib.SoliditySolution memory) {
        if (intent.intentPayload.enumVariant == IntentTypesLib.IntentPayloadEnum.Transact) {
            IntentTypesLib.IntentPayloadTransactData memory transactData =
                abi.decode(intent.intentPayload.data, (IntentTypesLib.IntentPayloadTransactData));
            if (transactData.sourceChainId == transactData.destinationChainId) {
                // Local transfer
                IntentTypesLib.SolverSolution memory solution = IntentTypesLib.SolverSolution({
                    solution: IntentTypesLib.SoliditySolutionType({
                        enumVariant: IntentTypesLib.SolutionTypeEnum.LocalTransfer,
                        data: bytes("")
                    }),
                    sourceChainId: transactData.sourceChainId,
                    destinationChainId: transactData.destinationChainId,
                    tokenOut: transactData.tokenOut,
                    tokenIn: transactData.tokenIn,
                    amountIn: transactData.amountIn,
                    amountOut: transactData.amountOut
                });
                IntentTypesLib.SolverSolution[] memory solutions =
                    new IntentTypesLib.SolverSolution[](1);
                solutions[0] = solution;
                IntentTypesLib.SoliditySolutionTransactSolutionData memory transactSolutionData =
                    IntentTypesLib.SoliditySolutionTransactSolutionData({ solutions: solutions });
                return IntentTypesLib.SoliditySolution({
                    enumVariant: IntentTypesLib.SoliditySolutionEnum.TransactSolution,
                    data: abi.encode(transactSolutionData)
                });
            } else {
                // Cross chain transfer
                // Get the domain ids based on the source and destination chain ids
                IntentTypesLib.DomainId[] memory sourceDomainIds =
                    intentProcessor.getDomainIds(transactData.sourceChainId);
                IntentTypesLib.DomainId[] memory destinationDomainIds =
                    intentProcessor.getDomainIds(transactData.destinationChainId);
                IntentTypesLib.LiquidityNetworkMockedLNData memory liquidityNetworkData =
                IntentTypesLib.LiquidityNetworkMockedLNData({
                    sourceChainId: sourceDomainIds[0].domainId,
                    destinationChainId: destinationDomainIds[0].domainId,
                    filler: bytes32(uint256(1)),
                    claimer: bytes32(uint256(2)),
                    stableCoinAddress: bytes32(0)
                });
                IntentTypesLib.SolutionTypeCrossChainData memory crossChainData = IntentTypesLib
                    .SolutionTypeCrossChainData({
                    liquidityNetwork: IntentTypesLib.SolidityLiquidityNetwork({
                        enumVariant: IntentTypesLib.LiquidityNetworkEnum.MockedLN,
                        data: abi.encode(liquidityNetworkData)
                    })
                });
                IntentTypesLib.SolverSolution memory solution = IntentTypesLib.SolverSolution({
                    solution: IntentTypesLib.SoliditySolutionType({
                        enumVariant: IntentTypesLib.SolutionTypeEnum.CrossChain,
                        data: abi.encode(crossChainData)
                    }),
                    sourceChainId: transactData.sourceChainId,
                    tokenIn: transactData.tokenIn,
                    amountIn: transactData.amountIn,
                    amountOut: transactData.amountOut,
                    tokenOut: transactData.tokenOut,
                    destinationChainId: transactData.destinationChainId
                });
                IntentTypesLib.SolverSolution[] memory solutions =
                    new IntentTypesLib.SolverSolution[](1);
                solutions[0] = solution;
                IntentTypesLib.SoliditySolutionTransactSolutionData memory transactSolutionData =
                    IntentTypesLib.SoliditySolutionTransactSolutionData({ solutions: solutions });
                return IntentTypesLib.SoliditySolution({
                    enumVariant: IntentTypesLib.SoliditySolutionEnum.TransactSolution,
                    data: abi.encode(transactSolutionData)
                });
            }
        } else {
            revert("Invalid intent type");
        }
    }

    function deserializeAcknowledgementMetadata(
        bytes memory message
    ) internal pure returns (IntentTypesLib.SolidityAcknowledgementMetadata memory) {
        IntentTypesLib.SolidityIntentProcessorBoundMessage memory intentProcessorBoundMessage =
            abi.decode(message, (IntentTypesLib.SolidityIntentProcessorBoundMessage));
        IntentTypesLib.IntentProcessorBoundMessageAcknowledgementData memory ackData = abi.decode(
            intentProcessorBoundMessage.data,
            (IntentTypesLib.IntentProcessorBoundMessageAcknowledgementData)
        );
        return ackData.metadata;
    }
}
